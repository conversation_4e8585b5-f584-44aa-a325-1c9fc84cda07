# Her Diary - 私人日记管理系统
## 专业项目开发指导文档

### 📋 项目概述

**Her Diary** 是一个现代化的个人日记管理系统，采用前后端分离架构。项目旨在为初学Python后端开发者提供一个真实的全栈开发实践环境，同时创造一个美观、实用的日记应用。

#### 🎯 项目目标
- **技能提升**: 掌握Flask框架、RESTful API设计、数据库操作
- **协作体验**: 理解前后端分离开发模式和API契约的重要性
- **实用价值**: 打造一个个人化的日记管理工具
- **代码质量**: 培养良好的编程习惯和代码规范

#### 🏗️ 系统架构
```
┌─────────────────┐    HTTP/HTTPS     ┌─────────────────┐
│   Frontend      │ ◄────────────────► │   Backend       │
│   Vue 3         │    RESTful API    │   Flask         │
│   Element Plus  │                   │   SQLAlchemy    │
└─────────────────┘                   └─────────────────┘
                                                │
                                                ▼
                                       ┌─────────────────┐
                                       │   Database      │
                                       │   SQLite/MySQL  │
                                       └─────────────────┘
```

---

### 🛠️ 技术栈详解

#### 前端技术栈
| 技术 | 版本 | 用途 | 学习资源 |
|------|------|------|----------|
| **Vue 3** | ^3.3.0 | 核心框架，使用Composition API | [Vue 3 官方文档](https://vuejs.org/) |
| **Element Plus** | ^2.4.0 | UI组件库 | [Element Plus 文档](https://element-plus.org/) |
| **Vue Router** | ^4.2.0 | 路由管理 | [Vue Router 文档](https://router.vuejs.org/) |
| **Pinia** | ^2.1.0 | 状态管理 | [Pinia 文档](https://pinia.vuejs.org/) |
| **Axios** | ^1.5.0 | HTTP客户端 | [Axios 文档](https://axios-http.com/) |
| **Vite** | ^4.4.0 | 构建工具 | [Vite 文档](https://vitejs.dev/) |
| **TypeScript** | ^5.0.0 | 类型检查(可选) | [TypeScript 文档](https://www.typescriptlang.org/) |

#### 后端技术栈
| 技术 | 版本 | 用途 | 学习资源 |
|------|------|------|----------|
| **Flask** | ^2.3.0 | Web框架 | [Flask 文档](https://flask.palletsprojects.com/) |
| **Flask-SQLAlchemy** | ^3.0.0 | ORM | [SQLAlchemy 文档](https://docs.sqlalchemy.org/) |
| **Flask-JWT-Extended** | ^4.5.0 | JWT认证 | [JWT Extended 文档](https://flask-jwt-extended.readthedocs.io/) |
| **Flask-CORS** | ^4.0.0 | 跨域处理 | [Flask-CORS 文档](https://flask-cors.readthedocs.io/) |
| **Flask-Migrate** | ^4.0.0 | 数据库迁移 | [Flask-Migrate 文档](https://flask-migrate.readthedocs.io/) |
| **Werkzeug** | ^2.3.0 | 密码加密 | [Werkzeug 文档](https://werkzeug.palletsprojects.com/) |
| **Marshmallow** | ^3.20.0 | 序列化/验证 | [Marshmallow 文档](https://marshmallow.readthedocs.io/) |

---

### 🎨 设计系统

#### 色彩规范 (Color Palette)
```css
/* 主色调 */
--primary-color: #C8A2C8;           /* 柔和紫 */
--primary-light: #E6D7E6;           /* 浅紫色 */
--primary-dark: #A888A8;            /* 深紫色 */

/* 辅助色 */
--accent-color: #FFB6C1;            /* 柔和粉 */
--accent-light: #FFDBDF;            /* 浅粉色 */
--accent-dark: #FF9AAF;             /* 深粉色 */

/* 中性色 */
--background-color: #F5F7FA;        /* 背景色 */
--surface-color: #FFFFFF;           /* 卡片背景 */
--text-primary: #303133;            /* 主要文本 */
--text-secondary: #606266;          /* 次要文本 */
--text-placeholder: #909399;        /* 占位文本 */
--border-color: #DCDFE6;            /* 边框色 */

/* 状态色 */
--success-color: #67C23A;           /* 成功 */
--warning-color: #E6A23C;           /* 警告 */
--danger-color: #F56C6C;            /* 错误 */
--info-color: #909399;              /* 信息 */

/* 渐变色 */
--gradient-primary: linear-gradient(135deg, #C8A2C8 0%, #FFB6C1 100%);
--gradient-card: linear-gradient(145deg, #FFFFFF 0%, #F5F7FA 100%);
```

#### 字体规范 (Typography)
```css
/* 字体族 */
--font-family-primary: 'Noto Sans SC', sans-serif;     /* 主要字体 */
--font-family-secondary: 'Source Code Pro', monospace; /* 代码字体 */

/* 字体大小 */
--font-size-xs: 12px;    /* 小字体 */
--font-size-sm: 14px;    /* 小号字体 */
--font-size-base: 16px;  /* 基础字体 */
--font-size-lg: 18px;    /* 大字体 */
--font-size-xl: 20px;    /* 超大字体 */
--font-size-2xl: 24px;   /* 标题字体 */
--font-size-3xl: 30px;   /* 大标题 */

/* 行高 */
--line-height-tight: 1.25;
--line-height-normal: 1.5;
--line-height-loose: 1.75;
```

#### 间距规范 (Spacing)
```css
--spacing-xs: 4px;
--spacing-sm: 8px;
--spacing-md: 16px;
--spacing-lg: 24px;
--spacing-xl: 32px;
--spacing-2xl: 48px;
--spacing-3xl: 64px;
```

#### 组件规范
1. **卡片阴影**: `box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)`
2. **圆角**: 基础圆角 `8px`，大圆角 `16px`
3. **动画时长**: 快速 `0.2s`，正常 `0.3s`，慢速 `0.5s`
4. **缓动函数**: `cubic-bezier(0.4, 0, 0.2, 1)`

---

### 🗄️ 数据库设计

#### ER图关系
```
Users ||--o{ Diaries : "一对多"
Diaries }o--o{ Tags : "多对多" (through DiaryTags)
Users ||--o{ UserSettings : "一对一"
```

#### 数据表结构

##### Users 用户表
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(80) UNIQUE NOT NULL,
    email VARCHAR(120) UNIQUE,
    password_hash VARCHAR(128) NOT NULL,
    avatar_url VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP
);
```

##### Diaries 日记表
```sql
CREATE TABLE diaries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    mood VARCHAR(20) DEFAULT 'neutral',
    weather VARCHAR(20) DEFAULT 'unknown',
    location VARCHAR(100),
    is_private BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);
```

##### Tags 标签表
```sql
CREATE TABLE tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    name VARCHAR(50) NOT NULL,
    color VARCHAR(7) DEFAULT '#C8A2C8',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    UNIQUE(user_id, name)
);
```

##### DiaryTags 日记标签关联表
```sql
CREATE TABLE diary_tags (
    diary_id INTEGER,
    tag_id INTEGER,
    PRIMARY KEY (diary_id, tag_id),
    FOREIGN KEY (diary_id) REFERENCES diaries (id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags (id) ON DELETE CASCADE
);
```

##### UserSettings 用户设置表
```sql
CREATE TABLE user_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER UNIQUE NOT NULL,
    theme VARCHAR(20) DEFAULT 'light',
    language VARCHAR(10) DEFAULT 'zh-CN',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
    notifications_enabled BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);
```

---

### 🔌 API接口规范

#### 基础配置
- **Base URL**: `http://127.0.0.1:5000/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: `Authorization: Bearer <JWT_TOKEN>`
- **字符编码**: `UTF-8`

#### 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2025-07-29T10:30:00Z",
  "request_id": "req_1234567890"
}
```

#### 状态码规范
| HTTP状态码 | 业务码 | 含义 | 示例场景 |
|-----------|--------|------|----------|
| 200 | 200 | 请求成功 | 获取数据成功 |
| 201 | 201 | 创建成功 | 新建日记成功 |
| 400 | 400 | 请求参数错误 | 缺少必填字段 |
| 401 | 401 | 未授权 | JWT token无效 |
| 403 | 403 | 禁止访问 | 访问他人日记 |
| 404 | 404 | 资源不存在 | 日记ID不存在 |
| 409 | 409 | 资源冲突 | 用户名已存在 |
| 422 | 422 | 参数校验失败 | 邮箱格式错误 |
| 500 | 500 | 服务器内部错误 | 数据库连接失败 |

#### 详细接口文档

##### 1. 用户认证模块

###### 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "alice",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "code": 201,
  "message": "注册成功",
  "data": {
    "user_id": 1,
    "username": "alice",
    "email": "<EMAIL>"
  }
}
```

###### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "alice",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
    "expires_in": 3600,
    "user": {
      "id": 1,
      "username": "alice",
      "email": "<EMAIL>",
      "avatar_url": null
    }
  }
}
```

###### 刷新Token
```http
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>
```

###### 用户登出
```http
POST /api/v1/auth/logout
Authorization: Bearer <access_token>
```

##### 2. 用户信息模块

###### 获取用户信息
```http
GET /api/v1/users/profile
Authorization: Bearer <access_token>
```

###### 更新用户信息
```http
PUT /api/v1/users/profile
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "avatar_url": "https://example.com/avatar.jpg"
}
```

###### 修改密码
```http
PUT /api/v1/users/password
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "old_password": "oldpass123",
  "new_password": "newpass123"
}
```

##### 3. 日记管理模块

###### 获取日记列表
```http
GET /api/v1/diaries?page=1&limit=10&mood=happy&tag=旅行&search=关键词
Authorization: Bearer <access_token>
```

**查询参数**:
- `page`: 页码，默认1
- `limit`: 每页数量，默认10，最大100
- `mood`: 心情筛选
- `tag`: 标签筛选
- `search`: 搜索关键词
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)
- `order_by`: 排序字段 (created_at, updated_at, title)
- `order`: 排序方向 (asc, desc)

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "diaries": [
      {
        "id": 1,
        "title": "美好的一天",
        "content": "今天天气很好...",
        "mood": "happy",
        "weather": "sunny",
        "location": "北京",
        "tags": [
          {"id": 1, "name": "生活", "color": "#C8A2C8"}
        ],
        "created_at": "2025-07-29T10:30:00Z",
        "updated_at": "2025-07-29T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_items": 47,
      "items_per_page": 10,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

###### 获取单篇日记
```http
GET /api/v1/diaries/{diary_id}
Authorization: Bearer <access_token>
```

###### 创建日记
```http
POST /api/v1/diaries
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "title": "今天的感想",
  "content": "# 标题\n\n内容...",
  "mood": "happy",
  "weather": "sunny",
  "location": "上海",
  "tag_ids": [1, 2, 3],
  "is_private": true
}
```

###### 更新日记
```http
PUT /api/v1/diaries/{diary_id}
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "title": "修改后的标题",
  "content": "修改后的内容",
  "mood": "calm",
  "tag_ids": [1, 3]
}
```

###### 删除日记
```http
DELETE /api/v1/diaries/{diary_id}
Authorization: Bearer <access_token>
```

##### 4. 标签管理模块

###### 获取标签列表
```http
GET /api/v1/tags
Authorization: Bearer <access_token>
```

###### 创建标签
```http
POST /api/v1/tags
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "工作",
  "color": "#FF6B6B"
}
```

###### 更新标签
```http
PUT /api/v1/tags/{tag_id}
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "学习",
  "color": "#4ECDC4"
}
```

###### 删除标签
```http
DELETE /api/v1/tags/{tag_id}
Authorization: Bearer <access_token>
```

##### 5. 仪表盘模块

###### 获取仪表盘统计数据
```http
GET /api/v1/dashboard/stats?period=month
Authorization: Bearer <access_token>
```

**查询参数**:
- `period`: 统计周期 (week, month, year, all)

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "summary": {
      "total_diaries": 47,
      "current_period_diaries": 12,
      "total_words": 15678,
      "average_words_per_diary": 333
    },
    "mood_distribution": {
      "happy": 15,
      "calm": 12,
      "sad": 8,
      "excited": 7,
      "anxious": 5
    },
    "weather_distribution": {
      "sunny": 18,
      "cloudy": 12,
      "rainy": 10,
      "snowy": 7
    },
    "writing_streak": {
      "current_streak": 7,
      "longest_streak": 21,
      "last_entry_date": "2025-07-29"
    },
    "tag_usage": [
      {"name": "生活", "count": 25},
      {"name": "工作", "count": 15},
      {"name": "旅行", "count": 10}
    ],
    "calendar_data": [
      {
        "date": "2025-07-29",
        "diary_count": 1,
        "mood": "happy"
      }
    ],
    "recent_diaries": [
      {
        "id": 47,
        "title": "最近的日记",
        "created_at": "2025-07-29T10:30:00Z",
        "mood": "happy"
      }
    ]
  }
}
```

###### 获取日历数据
```http
GET /api/v1/dashboard/calendar?year=2025&month=7
Authorization: Bearer <access_token>
```

---

### 📁 后端项目结构

```
her-diary-backend/
├── app/
│   ├── __init__.py              # Flask应用工厂
│   ├── config.py                # 配置文件
│   ├── extensions.py            # 扩展初始化
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py              # 用户模型
│   │   ├── diary.py             # 日记模型
│   │   ├── tag.py               # 标签模型
│   │   └── user_settings.py     # 用户设置模型
│   ├── schemas/                 # Marshmallow序列化
│   │   ├── __init__.py
│   │   ├── user_schema.py
│   │   ├── diary_schema.py
│   │   └── tag_schema.py
│   ├── api/                     # API蓝图
│   │   ├── __init__.py
│   │   ├── auth.py              # 认证相关接口
│   │   ├── users.py             # 用户相关接口
│   │   ├── diaries.py           # 日记相关接口
│   │   ├── tags.py              # 标签相关接口
│   │   └── dashboard.py         # 仪表盘相关接口
│   ├── utils/                   # 工具函数
│   │   ├── __init__.py
│   │   ├── auth.py              # 认证工具
│   │   ├── validators.py        # 验证器
│   │   ├── decorators.py        # 装饰器
│   │   └── helpers.py           # 辅助函数
│   └── errors/                  # 错误处理
│       ├── __init__.py
│       ├── handlers.py          # 错误处理器
│       └── exceptions.py        # 自定义异常
├── migrations/                  # 数据库迁移文件
├── tests/                       # 测试文件
│   ├── __init__.py
│   ├── conftest.py             # pytest配置
│   ├── test_auth.py
│   ├── test_diaries.py
│   └── test_dashboard.py
├── requirements.txt             # 依赖包
├── requirements-dev.txt         # 开发依赖
├── .env.example                 # 环境变量示例
├── .gitignore
├── README.md
└── run.py                       # 应用入口
```

### 📁 前端项目结构

```
her-diary-frontend/
├── public/
│   ├── index.html
│   ├── favicon.ico
│   └── icons/                   # 应用图标
├── src/
│   ├── main.js                  # 应用入口
│   ├── App.vue                  # 根组件
│   ├── assets/                  # 静态资源
│   │   ├── styles/
│   │   │   ├── index.css        # 全局样式
│   │   │   ├── variables.css    # CSS变量
│   │   │   └── components.css   # 组件样式
│   │   ├── images/
│   │   └── icons/
│   ├── components/              # 公共组件
│   │   ├── common/
│   │   │   ├── AppHeader.vue
│   │   │   ├── AppSidebar.vue
│   │   │   ├── AppFooter.vue
│   │   │   └── LoadingSpinner.vue
│   │   ├── diary/
│   │   │   ├── DiaryCard.vue
│   │   │   ├── DiaryEditor.vue
│   │   │   ├── MoodSelector.vue
│   │   │   └── WeatherSelector.vue
│   │   └── dashboard/
│   │       ├── StatCard.vue
│   │       ├── MoodChart.vue
│   │       └── CalendarView.vue
│   ├── views/                   # 页面组件
│   │   ├── auth/
│   │   │   ├── LoginView.vue
│   │   │   └── RegisterView.vue
│   │   ├── diary/
│   │   │   ├── DiaryListView.vue
│   │   │   ├── DiaryDetailView.vue
│   │   │   └── DiaryEditView.vue
│   │   ├── dashboard/
│   │   │   └── DashboardView.vue
│   │   └── user/
│   │       └── ProfileView.vue
│   ├── router/                  # 路由配置
│   │   └── index.js
│   ├── stores/                  # Pinia状态管理
│   │   ├── auth.js
│   │   ├── diary.js
│   │   └── app.js
│   ├── api/                     # API调用
│   │   ├── request.js           # Axios配置
│   │   ├── auth.js
│   │   ├── diary.js
│   │   ├── tag.js
│   │   └── dashboard.js
│   ├── utils/                   # 工具函数
│   │   ├── date.js
│   │   ├── validation.js
│   │   ├── constants.js
│   │   └── helpers.js
│   └── types/                   # TypeScript类型定义(可选)
│       ├── auth.ts
│       ├── diary.ts
│       └── api.ts
├── tests/                       # 测试文件
├── .env.development            # 开发环境变量
├── .env.production             # 生产环境变量
├── vite.config.js              # Vite配置
├── package.json
├── tailwind.config.js          # Tailwind配置(可选)
├── .gitignore
└── README.md
```

---

### 🚀 开发任务分解

#### 阶段一：项目初始化与基础架构 (Week 1)

##### 前端任务 (难度: ⭐⭐)
- [ ] **项目搭建**
  - [ ] 使用 `npm create vue@latest` 创建Vue 3项目
  - [ ] 安装依赖: Element Plus, Vue Router, Pinia, Axios
  - [ ] 配置Vite构建工具和开发服务器
  - [ ] 设置ESLint和Prettier代码规范

- [ ] **基础架构**
  - [ ] 创建项目基本布局结构
  - [ ] 配置全局CSS样式和设计变量
  - [ ] 封装Axios请求库，添加拦截器
  - [ ] 配置Vue Router和路由守卫
  - [ ] 搭建Pinia状态管理结构

- [ ] **认证页面**
  - [ ] 设计并实现登录页面UI
  - [ ] 设计并实现注册页面UI
  - [ ] 添加表单验证和用户体验优化
  - [ ] 实现登录/注册的前端逻辑

##### 后端任务 (难度: ⭐⭐⭐)
- [ ] **项目搭建**
  - [ ] 创建Flask项目结构
  - [ ] 配置虚拟环境和依赖管理
  - [ ] 设置Flask应用工厂模式
  - [ ] 配置数据库连接和ORM

- [ ] **用户系统**
  - [ ] 设计User数据模型
  - [ ] 实现用户注册功能和密码加密
  - [ ] 实现JWT认证机制
  - [ ] 创建认证装饰器和中间件
  - [ ] 编写用户相关API接口

- [ ] **开发工具**
  - [ ] 配置Flask-Migrate数据库迁移
  - [ ] 设置开发环境配置
  - [ ] 使用Postman测试API接口
  - [ ] 编写基础的单元测试

**里程碑**: 完成用户注册、登录功能，前后端能够正常通信

#### 阶段二：核心功能开发 (Week 2-3)

##### 前端任务 (难度: ⭐⭐⭐)
- [ ] **日记功能**
  - [ ] 实现日记列表页面，支持分页和筛选
  - [ ] 创建日记详情页面，支持Markdown渲染
  - [ ] 开发日记编辑器，集成富文本编辑功能
  - [ ] 实现心情和天气选择组件
  - [ ] 添加标签管理功能

- [ ] **用户体验**
  - [ ] 实现Loading状态和骨架屏
  - [ ] 添加操作成功/失败的消息提示
  - [ ] 实现确认对话框和二次确认
  - [ ] 优化移动端响应式设计

- [ ] **数据管理**
  - [ ] 对接所有日记相关API
  - [ ] 实现本地数据缓存策略
  - [ ] 处理网络错误和重试机制
  - [ ] 优化数据更新和同步

##### 后端任务 (难度: ⭐⭐⭐⭐)
- [ ] **数据模型**
  - [ ] 设计Diary和Tag数据模型
  - [ ] 实现多对多关系映射
  - [ ] 创建数据迁移脚本
  - [ ] 设计数据库索引优化查询性能

- [ ] **日记API**
  - [ ] 实现日记CRUD操作的完整接口
  - [ ] 添加分页、搜索、筛选功能
  - [ ] 实现数据验证和序列化
  - [ ] 处理文件上传（可选：图片附件）

- [ ] **权限控制**
  - [ ] 确保用户只能操作自己的数据
  - [ ] 实现细粒度的权限检查
  - [ ] 添加API访问频率限制
  - [ ] 处理并发访问问题

- [ ] **数据完整性**
  - [ ] 添加数据库约束和级联删除
  - [ ] 实现数据备份和恢复机制
  - [ ] 编写数据验证器
  - [ ] 处理异常情况和回滚

**里程碑**: 完成日记的增删改查功能，支持标签管理

#### 阶段三：高级功能与优化 (Week 4)

##### 前端任务 (难度: ⭐⭐⭐⭐)
- [ ] **仪表盘开发**
  - [ ] 实现数据统计卡片组件
  - [ ] 集成图表库（Chart.js或ECharts）
  - [ ] 创建日历视图组件
  - [ ] 实现心情和天气统计图表

- [ ] **高级交互**
  - [ ] 添加拖拽排序功能
  - [ ] 实现无限滚动加载
  - [ ] 添加全文搜索功能
  - [ ] 实现数据导出功能

- [ ] **性能优化**
  - [ ] 实现组件懒加载
  - [ ] 优化图片加载和缓存
  - [ ] 添加PWA支持（可选）
  - [ ] 实现离线数据同步（可选）

##### 后端任务 (难度: ⭐⭐⭐⭐⭐)
- [ ] **仪表盘API**
  - [ ] 实现复杂的数据统计查询
  - [ ] 优化数据库查询性能
  - [ ] 实现缓存机制（Redis可选）
  - [ ] 创建数据聚合和分析功能

- [ ] **高级功能**
  - [ ] 实现全文搜索（SQLite FTS或Elasticsearch）
  - [ ] 添加数据导入导出功能
  - [ ] 实现API文档自动生成
  - [ ] 添加日志记录和监控

- [ ] **部署准备**
  - [ ] 配置生产环境设置
  - [ ] 实现数据库连接池
  - [ ] 添加安全headers和CORS配置
  - [ ] 编写部署脚本和文档

**里程碑**: 完成所有核心功能，系统可以正常使用

#### 阶段四：测试与部署 (Week 5)

##### 联合任务 (难度: ⭐⭐⭐)
- [ ] **测试**
  - [ ] 编写单元测试和集成测试
  - [ ] 进行用户验收测试
  - [ ] 性能测试和压力测试
  - [ ] 兼容性测试

- [ ] **部署**
  - [ ] 配置生产环境
  - [ ] 设置CI/CD流水线
  - [ ] 域名和SSL证书配置
  - [ ] 监控和日志系统搭建

---

### 🔧 开发环境配置

#### 后端环境配置

##### 1. Python环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

##### 2. requirements.txt
```txt
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0
Flask-Migrate==4.0.5
marshmallow==3.20.1
python-dotenv==1.0.0
Werkzeug==2.3.7
```

##### 3. requirements-dev.txt
```txt
pytest==7.4.2
pytest-cov==4.1.0
black==23.7.0
flake8==6.0.0
pre-commit==3.4.0
```

##### 4. 环境变量配置 (.env)
```env
# Flask配置
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# 数据库配置
DATABASE_URL=sqlite:///her_diary.db
# 或使用MySQL
# DATABASE_URL=mysql://username:password@localhost/her_diary

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_EXPIRES=3600

# 邮件配置（可选）
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# 其他配置
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216  # 16MB
```

#### 前端环境配置

##### 1. Node.js环境
```bash
# 检查Node.js版本（需要16+）
node --version
npm --version

# 创建Vue项目
npm create vue@latest her-diary-frontend
cd her-diary-frontend

# 安装依赖
npm install
```

##### 2. package.json依赖
```json
{
  "dependencies": {
    "vue": "^3.3.4",
    "vue-router": "^4.2.5",
    "pinia": "^2.1.6",
    "element-plus": "^2.4.1",
    "@element-plus/icons-vue": "^2.1.0",
    "axios": "^1.5.0",
    "@vueuse/core": "^10.4.1"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.4.0",
    "vite": "^4.4.9",
    "eslint": "^8.49.0",
    "prettier": "^3.0.3",
    "@vue/eslint-config-prettier": "^8.0.0"
  }
}
```

##### 3. vite.config.js
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:5000',
        changeOrigin: true
      }
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/assets/styles/variables.scss";`
      }
    }
  }
})
```

##### 4. 环境变量配置
**.env.development**
```env
VITE_APP_TITLE=Her Diary
VITE_API_BASE_URL=http://127.0.0.1:5000/api/v1
VITE_APP_VERSION=1.0.0
```

**.env.production**
```env
VITE_APP_TITLE=Her Diary
VITE_API_BASE_URL=https://api.yourdomain.com/api/v1
VITE_APP_VERSION=1.0.0
```

---

### 📋 代码规范

#### 后端代码规范

##### 1. Python代码风格
```python
# 使用类型注解
from typing import List, Optional, Dict, Any

def get_user_diaries(
    user_id: int, 
    page: int = 1, 
    limit: int = 10
) -> Dict[str, Any]:
    """获取用户日记列表
    
    Args:
        user_id: 用户ID
        page: 页码
        limit: 每页数量
        
    Returns:
        包含日记列表和分页信息的字典
        
    Raises:
        ValueError: 当参数无效时
    """
    if limit > 100:
        raise ValueError("每页最多100条记录")
    
    # 实现逻辑...
    return {"diaries": [], "pagination": {}}
```

##### 2. API接口规范
```python
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from marshmallow import ValidationError

diary_bp = Blueprint('diaries', __name__)

@diary_bp.route('/diaries', methods=['POST'])
@jwt_required()
def create_diary():
    """创建新日记"""
    try:
        # 获取当前用户
        current_user_id = get_jwt_identity()
        
        # 验证请求数据
        data = request.get_json()
        diary_schema = DiaryCreateSchema()
        validated_data = diary_schema.load(data)
        
        # 业务逻辑
        diary = diary_service.create_diary(
            user_id=current_user_id,
            **validated_data
        )
        
        return jsonify({
            'code': 201,
            'message': '创建成功',
            'data': diary_schema.dump(diary)
        }), 201
        
    except ValidationError as e:
        return jsonify({
            'code': 422,
            'message': '参数验证失败',
            'errors': e.messages
        }), 422
    except Exception as e:
        logger.error(f"创建日记失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': '服务器内部错误'
        }), 500
```

##### 3. 数据模型规范
```python
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from app.extensions import db

class Diary(db.Model):
    """日记模型"""
    __tablename__ = 'diaries'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    title = Column(String(200), nullable=False)
    content = Column(Text)
    mood = Column(String(20), default='neutral')
    weather = Column(String(20), default='unknown')
    is_private = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系定义
    user = relationship('User', backref='diaries')
    tags = relationship('Tag', secondary='diary_tags', backref='diaries')
    
    def __repr__(self):
        return f'<Diary {self.title}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'mood': self.mood,
            'weather': self.weather,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
```

#### 前端代码规范

##### 1. Vue组件规范
```vue
<template>
  <div class="diary-card">
    <el-card 
      class="diary-card__content" 
      shadow="hover"
      @click="handleCardClick"
    >
      <template #header>
        <div class="diary-card__header">
          <h3 class="diary-card__title">{{ diary.title }}</h3>
          <span class="diary-card__date">{{ formatDate(diary.created_at) }}</span>
        </div>
      </template>
      
      <div class="diary-card__body">
        <p class="diary-card__preview">{{ diary.content | truncate(100) }}</p>
        
        <div class="diary-card__meta">
          <el-tag
            v-for="tag in diary.tags"
            :key="tag.id"
            :color="tag.color"
            size="small"
            class="diary-card__tag"
          >
            {{ tag.name }}
          </el-tag>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { formatDate } from '@/utils/date'

// Props定义
const props = defineProps({
  diary: {
    type: Object,
    required: true,
    validator: (diary) => {
      return diary && typeof diary.id === 'number' && diary.title
    }
  }
})

// Emits定义
const emit = defineEmits(['click', 'delete'])

// 组合式API
const router = useRouter()

// 计算属性
const truncatedContent = computed(() => {
  return props.diary.content?.substring(0, 100) + '...'
})

// 方法
const handleCardClick = () => {
  router.push(`/diary/${props.diary.id}`)
  emit('click', props.diary)
}
</script>

<style lang="scss" scoped>
.diary-card {
  margin-bottom: var(--spacing-md);
  
  &__content {
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
  }
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  &__title {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
  }
  
  &__date {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
  }
  
  &__preview {
    color: var(--text-secondary);
    line-height: var(--line-height-normal);
    margin-bottom: var(--spacing-sm);
  }
  
  &__meta {
    display: flex;
    gap: var(--spacing-xs);
  }
  
  &__tag {
    border: none;
  }
}
</style>
```

##### 2. 状态管理规范
```javascript
// stores/diary.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { diaryAPI } from '@/api/diary'

export const useDiaryStore = defineStore('diary', () => {
  // 状态
  const diaries = ref([])
  const currentDiary = ref(null)
  const loading = ref(false)
  const pagination = ref({
    current_page: 1,
    total_pages: 0,
    total_items: 0,
    items_per_page: 10
  })

  // 计算属性
  const totalDiaries = computed(() => pagination.value.total_items)
  const hasMorePages = computed(() => 
    pagination.value.current_page < pagination.value.total_pages
  )

  // 方法
  const fetchDiaries = async (params = {}) => {
    try {
      loading.value = true
      const response = await diaryAPI.getDiaries(params)
      
      if (params.page === 1) {
        diaries.value = response.data.diaries
      } else {
        diaries.value.push(...response.data.diaries)
      }
      
      pagination.value = response.data.pagination
    } catch (error) {
      console.error('获取日记列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createDiary = async (diaryData) => {
    try {
      const response = await diaryAPI.createDiary(diaryData)
      diaries.value.unshift(response.data)
      return response.data
    } catch (error) {
      console.error('创建日记失败:', error)
      throw error
    }
  }

  const updateDiary = async (id, diaryData) => {
    try {
      const response = await diaryAPI.updateDiary(id, diaryData)
      const index = diaries.value.findIndex(d => d.id === id)
      if (index !== -1) {
        diaries.value[index] = { ...diaries.value[index], ...response.data }
      }
      return response.data
    } catch (error) {
      console.error('更新日记失败:', error)
      throw error
    }
  }

  const deleteDiary = async (id) => {
    try {
      await diaryAPI.deleteDiary(id)
      diaries.value = diaries.value.filter(d => d.id !== id)
    } catch (error) {
      console.error('删除日记失败:', error)
      throw error
    }
  }

  return {
    // 状态
    diaries,
    currentDiary,
    loading,
    pagination,
    
    // 计算属性
    totalDiaries,
    hasMorePages,
    
    // 方法
    fetchDiaries,
    createDiary,
    updateDiary,
    deleteDiary
  }
})
```

##### 3. API调用规范
```javascript
// api/request.js
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { code, message, data } = response.data
    
    if (code === 200 || code === 201) {
      return { data, message }
    } else {
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message))
    }
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          const authStore = useAuthStore()
          authStore.logout()
          router.push('/login')
          break
        case 403:
          ElMessage.error('没有权限访问该资源')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data.message || '网络错误')
      }
    } else {
      ElMessage.error('网络连接失败')
    }
    
    return Promise.reject(error)
  }
)

export default request
```

---

### 🧪 测试策略

#### 后端测试

##### 1. 单元测试示例
```python
# tests/test_diary.py
import pytest
from app import create_app, db
from app.models.user import User
from app.models.diary import Diary
from tests.conftest import TestConfig

class TestDiaryAPI:
    
    def test_create_diary_success(self, client, auth_headers):
        """测试成功创建日记"""
        data = {
            'title': '测试日记',
            'content': '这是一篇测试日记',
            'mood': 'happy',
            'weather': 'sunny'
        }
        
        response = client.post(
            '/api/v1/diaries',
            json=data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        assert response.json['code'] == 201
        assert response.json['data']['title'] == '测试日记'
    
    def test_create_diary_validation_error(self, client, auth_headers):
        """测试创建日记参数验证"""
        data = {
            'content': '缺少标题的日记'
        }
        
        response = client.post(
            '/api/v1/diaries',
            json=data,
            headers=auth_headers
        )
        
        assert response.status_code == 422
        assert 'title' in response.json['errors']
    
    def test_get_diaries_pagination(self, client, auth_headers, sample_diaries):
        """测试日记列表分页"""
        response = client.get(
            '/api/v1/diaries?page=1&limit=5',
            headers=auth_headers
        )
        
        assert response.status_code == 200
        assert len(response.json['data']['diaries']) <= 5
        assert 'pagination' in response.json['data']
```

##### 2. 集成测试示例
```python
# tests/test_integration.py
import pytest
from tests.conftest import TestConfig

class TestUserDiaryIntegration:
    
    def test_full_diary_workflow(self, client):
        """测试完整的日记操作流程"""
        # 1. 注册用户
        register_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'password123'
        }
        response = client.post('/api/v1/auth/register', json=register_data)
        assert response.status_code == 201
        
        # 2. 登录用户
        login_data = {
            'username': 'testuser',
            'password': 'password123'
        }
        response = client.post('/api/v1/auth/login', json=login_data)
        assert response.status_code == 200
        token = response.json['data']['access_token']
        
        # 3. 创建日记
        headers = {'Authorization': f'Bearer {token}'}
        diary_data = {
            'title': '集成测试日记',
            'content': '测试内容'
        }
        response = client.post('/api/v1/diaries', json=diary_data, headers=headers)
        assert response.status_code == 201
        diary_id = response.json['data']['id']
        
        # 4. 获取日记
        response = client.get(f'/api/v1/diaries/{diary_id}', headers=headers)
        assert response.status_code == 200
        assert response.json['data']['title'] == '集成测试日记'
        
        # 5. 更新日记
        update_data = {'title': '更新后的标题'}
        response = client.put(f'/api/v1/diaries/{diary_id}', json=update_data, headers=headers)
        assert response.status_code == 200
        
        # 6. 删除日记
        response = client.delete(f'/api/v1/diaries/{diary_id}', headers=headers)
        assert response.status_code == 200
```

#### 前端测试

##### 1. 组件测试示例
```javascript
// tests/components/DiaryCard.test.js
import { mount } from '@vue/test-utils'
import { ElCard, ElTag } from 'element-plus'
import DiaryCard from '@/components/diary/DiaryCard.vue'

describe('DiaryCard', () => {
  const mockDiary = {
    id: 1,
    title: '测试日记',
    content: '这是一篇测试日记的内容',
    created_at: '2025-07-29T10:30:00Z',
    tags: [
      { id: 1, name: '生活', color: '#C8A2C8' }
    ]
  }

  it('should render diary information correctly', () => {
    const wrapper = mount(DiaryCard, {
      props: { diary: mockDiary },
      global: {
        components: { ElCard, ElTag }
      }
    })

    expect(wrapper.find('.diary-card__title').text()).toBe('测试日记')
    expect(wrapper.find('.diary-card__preview').text()).toContain('这是一篇测试日记')
    expect(wrapper.find('.diary-card__tag').text()).toBe('生活')
  })

  it('should emit click event when card is clicked', async () => {
    const wrapper = mount(DiaryCard, {
      props: { diary: mockDiary },
      global: {
        components: { ElCard, ElTag }
      }
    })

    await wrapper.find('.diary-card__content').trigger('click')
    expect(wrapper.emitted().click).toBeTruthy()
    expect(wrapper.emitted().click[0]).toEqual([mockDiary])
  })
})
```

##### 2. 状态管理测试
```javascript
// tests/stores/diary.test.js
import { setActivePinia, createPinia } from 'pinia'
import { useDiaryStore } from '@/stores/diary'
import * as diaryAPI from '@/api/diary'

// Mock API
vi.mock('@/api/diary')

describe('Diary Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should fetch diaries successfully', async () => {
    const mockResponse = {
      data: {
        diaries: [
          { id: 1, title: '日记1' },
          { id: 2, title: '日记2' }
        ],
        pagination: { current_page: 1, total_pages: 1 }
      }
    }
    
    diaryAPI.getDiaries.mockResolvedValue(mockResponse)
    
    const store = useDiaryStore()
    await store.fetchDiaries()
    
    expect(store.diaries).toHaveLength(2)
    expect(store.diaries[0].title).toBe('日记1')
  })

  it('should handle create diary', async () => {
    const newDiary = { title: '新日记', content: '内容' }
    const mockResponse = { data: { id: 3, ...newDiary } }
    
    diaryAPI.createDiary.mockResolvedValue(mockResponse)
    
    const store = useDiaryStore()
    const result = await store.createDiary(newDiary)
    
    expect(result.id).toBe(3)
    expect(store.diaries[0]).toEqual(result)
  })
})
```

---

### 🚀 部署指南

#### 开发环境部署

##### 后端启动
```bash
# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export FLASK_APP=run.py
export FLASK_ENV=development

# 初始化数据库
flask db init
flask db migrate -m "Initial migration"
flask db upgrade

# 启动开发服务器
flask run --host=0.0.0.0 --port=5000
```

##### 前端启动
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

#### 生产环境部署

##### 1. Docker部署
**后端Dockerfile**
```dockerfile
FROM python:3.9

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["gunicorn", "--bind", "0.0.0.0:5000", "run:app"]
```

**前端Dockerfile**
```dockerfile
FROM node:18 AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

**docker-compose.yml**
```yaml
version: '3.8'

services:
  backend:
    build: ./her-diary-backend
    environment:
      - DATABASE_URL=**********************************/her_diary
      - JWT_SECRET_KEY=your-production-secret
    depends_on:
      - db
    ports:
      - "5000:5000"

  frontend:
    build: ./her-diary-frontend
    ports:
      - "80:80"
    depends_on:
      - backend

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=her_diary
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

##### 2. 传统部署

**Nginx配置**
```nginx
server {
    listen 80;
    server_name yourdomain.com;

    # 前端静态文件
    location / {
        root /var/www/her-diary-frontend/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

**Gunicorn配置 (gunicorn.conf.py)**
```python
bind = "127.0.0.1:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 50
preload_app = True
timeout = 30
keepalive = 2
```

---

### 📈 性能优化

#### 后端性能优化

##### 1. 数据库优化
```python
# 索引优化
class Diary(db.Model):
    __tablename__ = 'diaries'
    
    # 添加复合索引
    __table_args__ = (
        db.Index('idx_user_created', 'user_id', 'created_at'),
        db.Index('idx_mood_weather', 'mood', 'weather'),
        db.Index('idx_title_content', 'title', 'content'),  # 全文搜索
    )
    
    # 懒加载关系
    tags = relationship('Tag', secondary='diary_tags', lazy='select')

# 查询优化
def get_user_diaries_optimized(user_id: int, page: int = 1, limit: int = 10):
    """优化的日记查询"""
    return db.session.query(Diary)\
        .options(
            joinedload(Diary.tags),  # 预加载标签
            selectinload(Diary.user)  # 预加载用户信息
        )\
        .filter(Diary.user_id == user_id)\
        .order_by(Diary.created_at.desc())\
        .paginate(
            page=page, 
            per_page=limit, 
            error_out=False
        )
```

##### 2. 缓存策略
```python
from flask_caching import Cache
import redis

# Redis缓存配置
cache = Cache()
redis_client = redis.Redis(host='localhost', port=6379, db=0)

def get_user_stats_cached(user_id: int):
    """缓存用户统计数据"""
    cache_key = f"user_stats:{user_id}"
    
    # 尝试从缓存获取
    cached_data = redis_client.get(cache_key)
    if cached_data:
        return json.loads(cached_data)
    
    # 计算统计数据
    stats = calculate_user_stats(user_id)
    
    # 存入缓存，5分钟过期
    redis_client.setex(cache_key, 300, json.dumps(stats))
    
    return stats

@cache.memoize(timeout=300)
def get_mood_distribution(user_id: int):
    """缓存心情分布数据"""
    return db.session.query(
        Diary.mood, 
        func.count(Diary.id).label('count')
    ).filter(
        Diary.user_id == user_id
    ).group_by(Diary.mood).all()
```

##### 3. API限流
```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["1000 per hour"]
)

@diary_bp.route('/diaries', methods=['POST'])
@limiter.limit("10 per minute")  # 每分钟最多创建10篇日记
@jwt_required()
def create_diary():
    # 创建日记逻辑
    pass

@diary_bp.route('/diaries', methods=['GET'])
@limiter.limit("100 per minute")  # 每分钟最多查询100次
@jwt_required()
def get_diaries():
    # 获取日记列表逻辑
    pass
```

#### 前端性能优化

##### 1. 代码分割和懒加载
```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/LoginView.vue')
    },
    {
      path: '/diary',
      name: 'Diary',
      component: () => import('@/views/diary/DiaryListView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/diary/:id',
      name: 'DiaryDetail',
      component: () => import('@/views/diary/DiaryDetailView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import('@/views/dashboard/DashboardView.vue'),
      meta: { requiresAuth: true }
    }
  ]
})
```

##### 2. 组件优化
```vue
<template>
  <div class="diary-list">
    <!-- 虚拟滚动优化长列表性能 -->
    <el-virtual-list
      :data="diaries"
      :height="600"
      :item-size="120"
      v-slot="{ item }"
    >
      <DiaryCard 
        :key="item.id"
        :diary="item"
        @click="handleDiaryClick"
      />
    </el-virtual-list>
    
    <!-- 无限滚动 -->
    <el-infinite-scroll
      @load="loadMoreDiaries"
      :disabled="loading"
      :finished="!hasMore"
    >
      <template #loading>
        <div class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>
      </template>
    </el-infinite-scroll>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useDiaryStore } from '@/stores/diary'
import DiaryCard from '@/components/diary/DiaryCard.vue'

const diaryStore = useDiaryStore()
const loading = ref(false)

// 计算属性缓存
const diaries = computed(() => diaryStore.diaries)
const hasMore = computed(() => diaryStore.hasMorePages)

// 防抖搜索
const searchDebounced = useDebounceFn((keyword) => {
  diaryStore.searchDiaries(keyword)
}, 300)

// 懒加载更多数据
const loadMoreDiaries = async () => {
  if (loading.value || !hasMore.value) return
  
  loading.value = true
  try {
    await diaryStore.fetchDiaries({
      page: diaryStore.pagination.current_page + 1
    })
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  diaryStore.fetchDiaries()
})
</script>
```

##### 3. 缓存策略
```javascript
// utils/cache.js
class CacheManager {
  constructor(maxAge = 5 * 60 * 1000) { // 默认5分钟
    this.cache = new Map()
    this.maxAge = maxAge
  }

  set(key, value) {
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    })
  }

  get(key) {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() - item.timestamp > this.maxAge) {
      this.cache.delete(key)
      return null
    }

    return item.value
  }

  clear() {
    this.cache.clear()
  }
}

export const diaryCache = new CacheManager()

// api/diary.js
import { diaryCache } from '@/utils/cache'

export const getDiaries = async (params = {}) => {
  const cacheKey = `diaries:${JSON.stringify(params)}`
  
  // 尝试从缓存获取
  const cached = diaryCache.get(cacheKey)
  if (cached) {
    return cached
  }

  // 请求API
  const response = await request.get('/diaries', { params })
  
  // 存入缓存
  diaryCache.set(cacheKey, response)
  
  return response
}
```

---

### 🔒 安全策略

#### 后端安全

##### 1. 输入验证和过滤
```python
from marshmallow import Schema, fields, validate, ValidationError
import bleach

class DiaryCreateSchema(Schema):
    title = fields.Str(
        required=True,
        validate=validate.Length(min=1, max=200),
        error_messages={'required': '标题不能为空'}
    )
    content = fields.Str(
        validate=validate.Length(max=10000),
        missing=''
    )
    mood = fields.Str(
        validate=validate.OneOf(['happy', 'sad', 'angry', 'calm', 'excited', 'anxious']),
        missing='neutral'
    )
    weather = fields.Str(
        validate=validate.OneOf(['sunny', 'cloudy', 'rainy', 'snowy', 'unknown']),
        missing='unknown'
    )

    def clean_content(self, value):
        """清理HTML内容，防止XSS攻击"""
        if value:
            # 允许的HTML标签
            allowed_tags = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'h1', 'h2', 'h3']
            return bleach.clean(value, tags=allowed_tags, strip=True)
        return value
```

##### 2. SQL注入防护
```python
from sqlalchemy import text

# 错误示例：容易受到SQL注入攻击
def search_diaries_unsafe(keyword):
    query = f"SELECT * FROM diaries WHERE title LIKE '%{keyword}%'"
    return db.session.execute(text(query)).fetchall()

# 正确示例：使用参数化查询
def search_diaries_safe(keyword):
    query = text("SELECT * FROM diaries WHERE title LIKE :keyword")
    return db.session.execute(query, {'keyword': f'%{keyword}%'}).fetchall()

# 更好的方式：使用ORM
def search_diaries_orm(keyword):
    return Diary.query.filter(
        Diary.title.contains(keyword)
    ).all()
```

##### 3. JWT安全配置
```python
from flask_jwt_extended import JWTManager
from datetime import timedelta

app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET_KEY')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
app.config['JWT_BLACKLIST_ENABLED'] = True
app.config['JWT_BLACKLIST_TOKEN_CHECKS'] = ['access', 'refresh']

jwt = JWTManager(app)

# Token黑名单
blacklist = set()

@jwt.token_in_blocklist_loader
def check_if_token_revoked(jwt_header, jwt_payload):
    return jwt_payload['jti'] in blacklist

@jwt.revoked_token_loader
def revoked_token_callback(jwt_header, jwt_payload):
    return jsonify({
        'code': 401,
        'message': 'Token已被吊销'
    }), 401

# 登出时将token加入黑名单
@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    jti = get_jwt()['jti']
    blacklist.add(jti)
    return jsonify({'code': 200, 'message': '登出成功'})
```

##### 4. CORS和安全头
```python
from flask_cors import CORS

# CORS配置
CORS(app, origins=[
    'http://localhost:3000',  # 开发环境
    'https://yourdomain.com'  # 生产环境
])

# 安全头中间件
@app.after_request
def after_request(response):
    # 防止点击劫持
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'
    
    # 防止MIME类型嗅探
    response.headers['X-Content-Type-Options'] = 'nosniff'
    
    # XSS防护
    response.headers['X-XSS-Protection'] = '1; mode=block'
    
    # HSTS (仅HTTPS)
    if request.is_secure:
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    
    return response
```

#### 前端安全

##### 1. XSS防护
```javascript
// utils/sanitize.js
import DOMPurify from 'dompurify'

export const sanitizeHTML = (dirty) => {
  return DOMPurify.sanitize(dirty, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'h1', 'h2', 'h3'],
    ALLOWED_ATTR: []
  })
}

export const escapeHTML = (text) => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

// 在组件中使用
<template>
  <div v-html="sanitizeHTML(diary.content)"></div>
</template>

<script setup>
import { sanitizeHTML } from '@/utils/sanitize'
</script>
```

##### 2. 敏感信息保护
```javascript
// utils/storage.js
class SecureStorage {
  constructor() {
    this.prefix = 'her_diary_'
  }

  setItem(key, value, encrypt = false) {
    const fullKey = this.prefix + key
    const data = encrypt ? this.encrypt(value) : value
    localStorage.setItem(fullKey, JSON.stringify(data))
  }

  getItem(key, decrypt = false) {
    const fullKey = this.prefix + key
    const data = localStorage.getItem(fullKey)
    
    if (!data) return null
    
    try {
      const parsed = JSON.parse(data)
      return decrypt ? this.decrypt(parsed) : parsed
    } catch {
      return null
    }
  }

  removeItem(key) {
    const fullKey = this.prefix + key
    localStorage.removeItem(fullKey)
  }

  clear() {
    Object.keys(localStorage)
      .filter(key => key.startsWith(this.prefix))
      .forEach(key => localStorage.removeItem(key))
  }

  // 简单的加密（生产环境应使用更强的加密）
  encrypt(text) {
    return btoa(encodeURIComponent(text))
  }

  decrypt(encoded) {
    return decodeURIComponent(atob(encoded))
  }
}

export const secureStorage = new SecureStorage()
```

##### 3. CSRF防护
```javascript
// api/request.js
import { secureStorage } from '@/utils/storage'

// 获取CSRF Token
const getCSRFToken = () => {
  return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
}

// 请求拦截器中添加CSRF Token
request.interceptors.request.use(
  (config) => {
    // 添加CSRF Token
    const csrfToken = getCSRFToken()
    if (csrfToken && ['post', 'put', 'delete'].includes(config.method)) {
      config.headers['X-CSRFToken'] = csrfToken
    }

    // 添加认证Token
    const token = secureStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  }
)
```

---

### 📊 监控和日志

#### 后端监控

##### 1. 日志配置
```python
import logging
from logging.handlers import RotatingFileHandler
import os

def setup_logging(app):
    """配置应用日志"""
    if not app.debug:
        # 创建日志目录
        if not os.path.exists('logs'):
            os.mkdir('logs')

        # 配置文件日志
        file_handler = RotatingFileHandler(
            'logs/her_diary.log',
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)

        app.logger.setLevel(logging.INFO)
        app.logger.info('Her Diary 启动')

# 业务日志记录
import functools

def log_api_call(func):
    """API调用日志装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            current_app.logger.info(
                f"API调用成功: {request.method} {request.path} "
                f"用时: {duration:.3f}s 用户: {get_jwt_identity()}"
            )
            
            return result
        except Exception as e:
            duration = time.time() - start_time
            
            current_app.logger.error(
                f"API调用失败: {request.method} {request.path} "
                f"用时: {duration:.3f}s 错误: {str(e)} 用户: {get_jwt_identity()}"
            )
            
            raise
    
    return wrapper
```

##### 2. 性能监控
```python
from flask import g
import time

@app.before_request
def before_request():
    g.start_time = time.time()

@app.after_request
def after_request(response):
    if hasattr(g, 'start_time'):
        duration = time.time() - g.start_time
        
        # 记录慢查询
        if duration > 1.0:  # 超过1秒的请求
            current_app.logger.warning(
                f"慢查询警告: {request.method} {request.path} "
                f"用时: {duration:.3f}s"
            )
    
    return response

# 数据库查询监控
from sqlalchemy import event
from sqlalchemy.engine import Engine

@event.listens_for(Engine, "before_cursor_execute")
def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    context._query_start_time = time.time()

@event.listens_for(Engine, "after_cursor_execute")
def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total = time.time() - context._query_start_time
    
    if total > 0.1:  # 超过100ms的SQL查询
        logger.warning(f"慢SQL查询: {total:.3f}s - {statement[:100]}...")
```

##### 3. 健康检查
```python
@app.route('/health')
def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        db.session.execute('SELECT 1')
        
        # 检查Redis连接（如果使用）
        # redis_client.ping()
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'database': 'connected'
        }), 200
        
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 503

@app.route('/metrics')
def metrics():
    """应用指标"""
    return jsonify({
        'users_count': User.query.count(),
        'diaries_count': Diary.query.count(),
        'tags_count': Tag.query.count(),
        'today_diaries': Diary.query.filter(
            Diary.created_at >= datetime.utcnow().date()
        ).count()
    })
```

#### 前端监控

##### 1. 错误监控
```javascript
// utils/errorHandler.js
class ErrorHandler {
  constructor() {
    this.setupGlobalErrorHandler()
    this.setupUnhandledRejectionHandler()
    this.setupVueErrorHandler()
  }

  setupGlobalErrorHandler() {
    window.addEventListener('error', (event) => {
      this.logError({
        type: 'javascript_error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      })
    })
  }

  setupUnhandledRejectionHandler() {
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        type: 'promise_rejection',
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        timestamp: new Date().toISOString(),
        url: window.location.href
      })
    })
  }

  setupVueErrorHandler() {
    const app = getCurrentInstance()?.appContext.app
    if (app) {
      app.config.errorHandler = (err, vm, info) => {
        this.logError({
          type: 'vue_error',
          message: err.message,
          stack: err.stack,
          info: info,
          timestamp: new Date().toISOString()
        })
      }
    }
  }

  logError(errorInfo) {
    // 发送到后端日志服务
    fetch('/api/v1/logs/frontend-error', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(errorInfo)
    }).catch(() => {
      // 如果发送失败，存储到本地
      console.error('Failed to send error log:', errorInfo)
    })
  }
}

export const errorHandler = new ErrorHandler()
```

##### 2. 性能监控
```javascript
// utils/performance.js
class PerformanceMonitor {
  constructor() {
    this.metrics = {}
    this.startTracking()
  }

  startTracking() {
    // 页面加载性能
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0]
        this.reportMetrics({
          type: 'page_load',
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          firstContentfulPaint: this.getFirstContentfulPaint(),
          timestamp: new Date().toISOString()
        })
      }, 1000)
    })
  }

  getFirstContentfulPaint() {
    const entries = performance.getEntriesByType('paint')
    const fcp = entries.find(entry => entry.name === 'first-contentful-paint')
    return fcp ? fcp.startTime : null
  }

  trackUserAction(action, startTime = Date.now()) {
    return {
      end: () => {
        const duration = Date.now() - startTime
        this.reportMetrics({
          type: 'user_action',
          action: action,
          duration: duration,
          timestamp: new Date().toISOString()
        })
      }
    }
  }

  reportMetrics(metrics) {
    // 发送性能数据到后端
    fetch('/api/v1/metrics/frontend', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(metrics)
    }).catch(console.error)
  }
}

export const performanceMonitor = new PerformanceMonitor()

// 在组件中使用
export default {
  async mounted() {
    const tracker = performanceMonitor.trackUserAction('diary_list_load')
    
    try {
      await this.loadDiaries()
    } finally {
      tracker.end()
    }
  }
}
```

---

### 🚀 优化建议与最佳实践

#### 开发最佳实践

##### 1. Git工作流
```bash
# 主分支保护
main -> 生产环境
develop -> 开发环境

# 功能分支命名规范
feature/用户认证
feature/日记编辑
bugfix/修复登录问题
hotfix/紧急修复安全漏洞

# 提交信息规范
feat: 添加日记搜索功能
fix: 修复分页显示错误
docs: 更新API文档
style: 优化登录页面样式
refactor: 重构用户认证逻辑
test: 添加日记模块单元测试
```

##### 2. 代码审查清单
```markdown
## 后端代码审查清单
- [ ] API接口是否遵循RESTful规范
- [ ] 是否有适当的参数验证
- [ ] 是否有错误处理和日志记录
- [ ] 数据库查询是否优化
- [ ] 是否有安全漏洞（SQL注入、XSS等）
- [ ] 是否编写了单元测试
- [ ] 代码是否符合PEP 8规范

## 前端代码审查清单
- [ ] 组件是否可复用
- [ ] 是否有适当的错误处理
- [ ] 性能是否优化（懒加载、缓存等）
- [ ] 是否响应式设计
- [ ] 是否符合无障碍访问标准
- [ ] 代码是否符合ESLint规范
- [ ] 是否有适当的类型检查
```

##### 3. 持续集成配置
**.github/workflows/ci.yml**
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  backend-test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        cd her-diary-backend
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run tests
      run: |
        cd her-diary-backend
        pytest --cov=app tests/
    
    - name: Code quality check
      run: |
        cd her-diary-backend
        flake8 app/
        black --check app/

  frontend-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: her-diary-frontend/package-lock.json
    
    - name: Install dependencies
      run: |
        cd her-diary-frontend
        npm ci
    
    - name: Run tests
      run: |
        cd her-diary-frontend
        npm run test:unit
    
    - name: Build application
      run: |
        cd her-diary-frontend
        npm run build
    
    - name: Code quality check
      run: |
        cd her-diary-frontend
        npm run lint

  deploy:
    needs: [backend-test, frontend-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production..."
        # 实际部署脚本
```

---

### 📚 学习资源和参考文档

#### 官方文档
- [Flask 官方文档](https://flask.palletsprojects.com/)
- [Vue 3 官方文档](https://vuejs.org/)
- [Element Plus 文档](https://element-plus.org/)
- [SQLAlchemy 文档](https://docs.sqlalchemy.org/)
