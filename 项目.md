

### **项目灵感来源**
```
我现在需要给初学Python后端的女朋友建造一个前端项目

女朋友目前是初学Python 3个月，正在学习Flask框架。我需要创建一个前端项目，让她编写后端接口对接前端页面。

我的想法是创建一个日记管理系统，因为她有记录日记的习惯。

系统的技术栈采用VUE3 + ELEMENT UI，因为我有这两个项目的编写经验。

前端页面要求美观实用，用户交互性好，动画流畅，页面的颜色主题采用粉色+紫色的柔和融合设计。

请你编写一份项目指导文档，构建开发任务 
```

### **需求分析与完善**

1.  **核心功能深化 (CRUD):**
    * **日记列表 (Read):** 不仅仅是标题列表，可以增加“卡片式”布局，每张卡片显示标题、日期、天气/心情图标、以及一小段正文预览。
    * **日记详情 (Read):** 展示完整的日记内容，支持 Markdown 渲染，这样她可以记录带格式的文本（比如加粗、列表、引用）。
    * **新建/编辑日记 (Create/Update):** 提供一个功能丰富的编辑器。除了标题和正文，可以增加一些有趣的元数据字段：
        * **心情选择:** (开心, 平静, 伤心, 激动...)，可以用一组可爱的 Emoji 或图标表示。
        * **天气记录:** (晴, 雨, 阴, 雪...)，同样使用图标。
        * **标签/分类 (Tags):** 允许她为日记打上多个标签，如 "旅行", "工作", "生活感悟" 等，方便未来筛选。
    * **删除日记 (Delete):** 需要有二次确认，防止误删。

2.  **增加用户系统 (Authentication):**
    * 日记是私密的，所以一个用户注册和登录系统是必不可少的。这对她来说是极好的练习，可以学习如何处理用户数据、密码加密、以及身份验证。
    * **功能点:** 注册、登录、登出、密码修改。
    * **技术点 (后端):** 这会让她接触到 `JWT (JSON Web Tokens)` 或 `Session` 的概念，这是现代 Web 应用必备的技能。

3.  **增加数据可视化/仪表盘 (Dashboard):**
    * 在用户登录后的首页，可以设置一个仪表盘。
    * **功能点:**
        * 一个日历视图，高亮显示有写日记的日期。
        * 统计数据卡片，如 “本月已记录 X 篇日记”、“最常出现的心情是 😊” 等。
        * 近期日记快速预览。
    * 这会让项目看起来非常专业，并且能让她练习更复杂的数据查询和聚合。

4.  **设计与交互优化:**
    * **颜色主题:** “粉色+紫色的柔和融合”非常棒。我们可以确定一个主色调（如 `#C8A2C8` 丁香紫）和一个辅助/点缀色（如 `#FFB6C1` 浅粉红），搭配大量的白色和浅灰色作为中性色，以保证页面的优雅和可读性。可以运用渐变色在背景或卡片上。
    * **动画:**
        * **页面切换:** 使用 Vue 的 `<transition>` 组件实现淡入淡出或轻微的滑动效果。
        * **加载状态:** 在请求 API 时，显示优雅的骨架屏 (Skeleton) 或 Loading 图标，而不是空白页面。
        * **交互反馈:** 按钮点击时有波纹或缩放效果；操作成功或失败时，在右上角弹出 Element Plus 的 `Notification` 或 `Message` 提示。
    * **字体:** 选择一款柔和、易读的免费字体，如“思源宋体”或“站酷快乐体”，能大大提升项目气质。

---

### **项目指导文档： "Her Diary" - 私人日记系统**

**1. 项目概述**

“Her Diary” 是一个为初学 Python 后端的开发者（你的女朋友）设计的实践项目。前端将由你使用 Vue 3 和 Element Plus 构建一个美观、交互流畅的单页应用 (SPA)。后端则由她使用 Flask 框架来开发提供数据支持的 API 接口。

这个项目的目标不仅仅是完成功能，更重要的是：
* **学习:** 让她在一个真实的项目场景中掌握 Flask 框架、API 设计和数据库操作。
* **协作:** 体验前后端分离的开发模式，理解 API “契约”的重要性。
* **成就感:** 亲手打造一个自己每天都能使用的、充满个性的应用。

**2. 技术栈**

* **前端 (你负责):**
    * **框架:** Vue 3 (Composition API)
    * **UI 库:** Element Plus
    * **HTTP 请求:** Axios
    * **路由:** Vue Router
    * **状态管理 (可选):** Pinia
    * **打包工具:** Vite

* **后端 (她负责):**
    * **框架:** Flask
    * **数据库:** SQLite (开发阶段简单易用) 或 PostgreSQL (更专业)
    * **ORM:** Flask-SQLAlchemy
    * **API 框架:** Flask-RESTful (或手动构建蓝图 Blueprint)
    * **用户认证:** JWT (推荐 PyJWT 库)
    * **密码处理:** Werkzeug security (用于哈希密码)
    * **API 测试工具 (推荐):** Postman / Insomnia

**3. 设计指南**

* **色彩方案:**
    * **主色 (Primary):** 柔和紫 `#C8A2C8` (用于按钮、标题、高亮边框)
    * **辅色 (Accent):** 柔和粉 `#FFB6C1` (用于点缀、标签、特殊提示)
    * **背景色:** 超浅灰 `#F5F7FA`
    * **卡片/容器背景:** 白色 `#FFFFFF`
    * **正文文本:** 深灰色 `#303133`
    * **提示文本:** 中灰色 `#909399`
    * **渐变:** 可在登录页背景或 Dashboard 卡片上使用主辅色的线性渐变。

* **交互动画:**
    * 所有异步操作（登录、加载日记）必须有加载状态提示。
    * 操作结果（保存成功、删除失败）通过 `ElNotification` 组件给予明确反馈。
    * 鼠标悬停在可点击元素上时，应有视觉变化（如颜色变深、轻微上浮）。

**4. 核心功能模块**

项目可以划分为以下几个核心模块：

* **模块一：用户认证**
    * 注册页面
    * 登录页面
    * 路由守卫（未登录用户不能访问日记页面）

* **模块二：日记管理 (CRUD)**
    * 日记列表页（卡片式展示）
    * 日记详情页（阅读模式）
    * 日记写作/编辑页（富文本或 Markdown 编辑器）

* **模块三：仪表盘 (Dashboard)**
    * 日历视图
    * 数据统计卡片

**5. API 接口“契约”文档 (前后端协作的关键)**

这是你们俩需要共同遵守的规范。后端需要实现这些接口，前端需要调用这些接口。

**基础 URL:** `http://127.0.0.1:5000/api/v1`
**认证方式:** 请求头中携带 `Authorization: Bearer <token>`

| 功能 | HTTP 方法 | URL | 请求体 (Request Body) | 成功响应 (Success Response) |
| :--- | :--- | :--- | :--- | :--- |
| **用户注册** | `POST` | `/users/register` | `{"username": "...", "password": "..."}` | `{"code": 201, "message": "注册成功"}` |
| **用户登录** | `POST` | `/users/login` | `{"username": "...", "password": "..."}` | `{"code": 200, "message": "登录成功", "data": {"token": "...", "user": {"id": 1, "username": "..."}}}` |
| **获取日记列表** | `GET` | `/diaries` | (无) | `{"code": 200, "data": [{"id": 1, "title": "...", "created_at": "...", "mood": "...", "weather": "..."}]}` |
| **新建日记** | `POST` | `/diaries` | `{"title": "...", "content": "...", "mood": "happy", "weather": "sunny", "tags": ["旅行"]}` | `{"code": 201, "message": "创建成功", "data": {"id": 2, ...}}` |
| **获取单篇日记** | `GET` | `/diaries/<int:diary_id>` | (无) | `{"code": 200, "data": {"id": 1, "title": "...", "content": "...", ...}}` |
| **更新日记** | `PUT` | `/diaries/<int:diary_id>` | `{"title": "...", "content": "...", ...}` | `{"code": 200, "message": "更新成功"}` |
| **删除日记** | `DELETE` | `/diaries/<int:diary_id>` | (无) | `{"code": 200, "message": "删除成功"}` |
| **获取仪表盘数据**| `GET` | `/dashboard/stats` | (无) | `{"code": 200, "data": {"total_diaries": 50, "mood_counts": {"happy": 20, ...}, "calendar_marks": ["2025-07-28", ...]}}`|

---

### **开发任务分解 (To-Do List)**

#### **第一阶段：项目搭建与用户系统**

**前端任务 (For You):**
1.  **[ ]** 使用 Vite 创建 Vue 3 项目。
2.  **[ ]** 安装并配置 `Element Plus`, `Vue Router`, `Axios`。
3.  **[ ]** 创建项目基本布局（Header, Main, Footer）。
4.  **[ ]** 创建登录页面和注册页面的静态组件。
5.  **[ ]** 封装 Axios，设置请求拦截器（自动添加 `token`）和响应拦截器（统一处理错误）。
6.  **[ ]** 对接登录和注册 API，实现登录/注册逻辑，并将获取的 `token` 存入 `localStorage` 或 `Pinia`。
7.  **[ ]** 配置 Vue Router 的导航守卫，实现未登录跳转到登录页。

**后端任务 (For Her):**
1.  **[ ]** 初始化 Flask 项目，配置好 `Flask-SQLAlchemy`。
2.  **[ ]** 设计 `User` 用户数据模型（Model），包含 `id`, `username`, `password_hash` 字段。
3.  **[ ]** 学习使用 `werkzeug.security` 对密码进行哈希加密和验证。
4.  **[ ]** 编写 `/api/v1/users/register` 接口的逻辑。
5.  **[ ]** 学习 JWT，编写 `/api/v1/users/login` 接口，在验证成功后生成并返回 `token`。
6.  **[ ]** 使用 Postman 等工具独立测试自己编写的两个接口。

#### **第二阶段：核心日记功能**

**前端任务 (For You):**
1.  **[ ]** 创建日记列表页，使用卡片布局展示日记。
2.  **[ ]** 创建日记详情页。
3.  **[ ]** 创建新建/编辑日记页面，集成一个 Markdown 编辑器（如 `v-md-editor`）。
4.  **[ ]** 对接获取日记列表、详情、新建、更新、删除的所有 API。
5.  **[ ]** 在所有数据请求处添加 Loading 状态（骨架屏或加载动画）。
6.  **[ ]** 实现删除操作前的 `ElMessageBox` 确认对话框。

**后端任务 (For Her):**
1.  **[ ]** 设计 `Diary` 日记和 `Tag` 标签的数据模型，并处理好它们之间的关联（多对多）。
2.  **[ ]** 编写一个装饰器或中间件，用于验证请求头中的 JWT `token`，保护需要登录才能访问的接口。
3.  **[ ]** 实现日记的增 (`POST /diaries`)、删 (`DELETE /diaries/<id>`)、改 (`PUT /diaries/<id>`)、查 (`GET /diaries`, `GET /diaries/<id>`) 的全部接口逻辑。
4.  **[ ]** 确保所有接口都与 `User` 模型关联，即一个用户只能操作自己的日记。
5.  **[ ]** 再次使用 Postman 充分测试所有日记相关的接口。

#### **第三阶段：锦上添花**

**前端任务 (For You):**
1.  **[ ]** 创建 Dashboard 页面。
2.  **[ ]** 使用 Element Plus 的 `Calendar` 组件，并根据 API 返回数据高亮日期。
3.  **[ ]** 制作统计数据卡片。
4.  **[ ]** 对接 `/dashboard/stats` 接口。
5.  **[ ]** 整体检查并优化所有页面的样式和动画效果，确保符合设计指南。

**后端任务 (For Her):**
1.  **[ ]** 编写 `/api/v1/dashboard/stats` 接口的逻辑。
2.  **[ ]** 这需要她学习更高级的数据库查询，如 `count`, `group by` 来统计心情数据，以及查询特定月份的所有日记日期。
3.  **[ ]** 学习并添加日记搜索功能（如按标题、内容、标签搜索）。

### **协作建议**

1.  **Git an GitHub:** 从第一天起就使用 Git 进行版本控制，并创建一个私有的 GitHub 仓库，你们俩都作为协作者。你负责 `main` 分支，她可以在自己的 `dev-backend` 分支上开发。
2.  **沟通 API:** 在她开始写后端代码前，你们俩要一起过一遍上面的 API “契约”文档，确保理解一致。
3.  **并行开发:** 在她开发后端接口时，你可以先用“假数据” (Mock Data) 来开发前端页面。这样你们就不用互相等待。
4.  **鼓励与耐心:** 对她来说，这可能是第一个完整的项目。多给她鼓励，耐心解答她的问题。当她成功用 Postman 调通第一个接口时，别忘了庆祝一下！

祝你们项目顺利，玩得开心！这会是一次非常有意义的共同经历。